import { Order, Position, Account, PerformanceMetrics, OrderType, BidAskPrice, PriceQuote, TickData, CandleData } from '@/types/trading';

// Trading constants
export const COMMISSION_PER_LOT = 7; // USD per lot
export const LEVERAGE = 100;
export const MARGIN_REQUIREMENT = 0.01; // 1% margin requirement

// Calculate position size in base currency
export function calculatePositionValue(size: number, price: number): number {
  return size * 100000 * price; // Standard lot = 100,000 units
}

// Calculate required margin for a position
export function calculateMargin(size: number, price: number): number {
  const positionValue = calculatePositionValue(size, price);
  return positionValue / LEVERAGE;
}

// Calculate unrealized P&L for a position
export function calculateUnrealizedPnL(position: Position, currentPrice: number): number {
  const priceDiff = position.type === 'buy'
    ? currentPrice - position.entryPrice
    : position.entryPrice - currentPrice;

  return priceDiff * position.size * 100000; // Convert to base currency
}

// Calculate realized P&L for a closed order
export function calculateRealizedPnL(order: Order): number {
  if (!order.exitPrice || order.status !== 'closed') {
    return 0;
  }

  const priceDiff = order.type === 'buy'
    ? order.exitPrice - order.entryPrice
    : order.entryPrice - order.exitPrice;

  const grossPnL = priceDiff * order.size * 100000;
  const commission = (order.commission || 0);

  return grossPnL - commission;
}

// Calculate commission for an order
export function calculateCommission(size: number): number {
  return size * COMMISSION_PER_LOT;
}

// Update account state based on positions and orders
export function updateAccount(
  baseAccount: Account,
  positions: Position[],
  orders: Order[]
): Account {
  let totalMargin = 0;
  let totalUnrealizedPnL = 0;
  let totalRealizedPnL = 0;
  let totalCommission = 0;

  // Calculate margin and unrealized P&L from open positions
  positions.forEach(position => {
    totalMargin += calculateMargin(position.size, position.entryPrice);
    totalUnrealizedPnL += position.unrealizedPnL;
  });

  // Calculate realized P&L from closed orders
  orders.forEach(order => {
    if (order.status === 'closed') {
      totalRealizedPnL += calculateRealizedPnL(order);
      totalCommission += order.commission || 0;
    }
  });

  const balance = baseAccount.balance + totalRealizedPnL;
  const equity = balance + totalUnrealizedPnL;
  const freeMargin = equity - totalMargin;
  const marginLevel = totalMargin > 0 ? (equity / totalMargin) * 100 : 0;

  return {
    balance,
    equity,
    margin: totalMargin,
    freeMargin,
    marginLevel,
    totalPnL: totalRealizedPnL + totalUnrealizedPnL,
    totalCommission
  };
}

// Check if an order can be executed (sufficient margin)
export function canExecuteOrder(
  account: Account,
  orderType: OrderType,
  size: number,
  price: number
): { canExecute: boolean; reason?: string } {
  const requiredMargin = calculateMargin(size, price);

  if (account.freeMargin < requiredMargin) {
    return {
      canExecute: false,
      reason: `Insufficient margin. Required: $${requiredMargin.toFixed(2)}, Available: $${account.freeMargin.toFixed(2)}`
    };
  }

  return { canExecute: true };
}

// Check if stop loss or take profit should be triggered
export function checkStopLossTakeProfit(
  position: Position,
  currentPrice: number
): { shouldClose: boolean; reason?: string } {
  if (position.stopLoss) {
    const stopLossTriggered = position.type === 'buy'
      ? currentPrice <= position.stopLoss
      : currentPrice >= position.stopLoss;

    if (stopLossTriggered) {
      return { shouldClose: true, reason: 'Stop Loss' };
    }
  }

  if (position.takeProfit) {
    const takeProfitTriggered = position.type === 'buy'
      ? currentPrice >= position.takeProfit
      : currentPrice <= position.takeProfit;

    if (takeProfitTriggered) {
      return { shouldClose: true, reason: 'Take Profit' };
    }
  }

  return { shouldClose: false };
}

// Calculate performance metrics
export function calculatePerformanceMetrics(orders: Order[]): PerformanceMetrics {
  const closedOrders = orders.filter(order => order.status === 'closed');

  if (closedOrders.length === 0) {
    return {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalPnL: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      profitFactor: 0,
      averageWin: 0,
      averageLoss: 0,
      largestWin: 0,
      largestLoss: 0
    };
  }

  const pnls = closedOrders.map(order => calculateRealizedPnL(order));
  const winningTrades = pnls.filter(pnl => pnl > 0);
  const losingTrades = pnls.filter(pnl => pnl < 0);

  const totalPnL = pnls.reduce((sum, pnl) => sum + pnl, 0);
  const totalWins = winningTrades.reduce((sum, pnl) => sum + pnl, 0);
  const totalLosses = Math.abs(losingTrades.reduce((sum, pnl) => sum + pnl, 0));

  // Calculate drawdown
  let runningPnL = 0;
  let peak = 0;
  let maxDrawdown = 0;

  pnls.forEach(pnl => {
    runningPnL += pnl;
    if (runningPnL > peak) {
      peak = runningPnL;
    }
    const drawdown = peak - runningPnL;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  });

  return {
    totalTrades: closedOrders.length,
    winningTrades: winningTrades.length,
    losingTrades: losingTrades.length,
    winRate: (winningTrades.length / closedOrders.length) * 100,
    totalPnL,
    maxDrawdown,
    sharpeRatio: calculateSharpeRatio(pnls),
    profitFactor: totalLosses > 0 ? totalWins / totalLosses : 0,
    averageWin: winningTrades.length > 0 ? totalWins / winningTrades.length : 0,
    averageLoss: losingTrades.length > 0 ? totalLosses / losingTrades.length : 0,
    largestWin: winningTrades.length > 0 ? Math.max(...winningTrades) : 0,
    largestLoss: losingTrades.length > 0 ? Math.min(...losingTrades) : 0
  };
}

// Calculate Sharpe ratio (simplified)
function calculateSharpeRatio(pnls: number[]): number {
  if (pnls.length < 2) return 0;

  const mean = pnls.reduce((sum, pnl) => sum + pnl, 0) / pnls.length;
  const variance = pnls.reduce((sum, pnl) => sum + Math.pow(pnl - mean, 2), 0) / pnls.length;
  const stdDev = Math.sqrt(variance);

  return stdDev > 0 ? mean / stdDev : 0;
}

// Format currency values
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
}

// Format percentage values
export function formatPercentage(value: number): string {
  return `${value.toFixed(2)}%`;
}

// Detect decimal precision from a number
export function detectDecimalPrecision(value: number): number {
  if (isNaN(value) || !isFinite(value)) return 5; // Default fallback

  const str = value.toString();
  const decimalIndex = str.indexOf('.');

  if (decimalIndex === -1) return 0; // No decimal places

  // Count decimal places, excluding trailing zeros
  const decimalPart = str.substring(decimalIndex + 1);
  const trimmed = decimalPart.replace(/0+$/, ''); // Remove trailing zeros

  return trimmed.length;
}

// Detect precision from an array of price values
export function detectPricePrecision(prices: number[]): number {
  if (prices.length === 0) return 5; // Default fallback

  const precisions = prices
    .filter(price => !isNaN(price) && isFinite(price))
    .map(price => detectDecimalPrecision(price))
    .filter(precision => precision > 0); // Only consider values with decimals

  if (precisions.length === 0) return 2; // Default for whole numbers

  // Use the maximum precision found, but cap at reasonable limits
  // Using spread with a very large array can cause a "Maximum call stack size exceeded" error.
  // Compute the max precision via a simple reduce to stay safe for large datasets.
  const maxPrecision = precisions.reduce((max, p) => (p > max ? p : max), precisions[0]);

  // Apply reasonable limits based on typical market data
  if (maxPrecision <= 2) return 2; // Stocks, indices
  if (maxPrecision <= 3) return 3; // Some commodities
  if (maxPrecision <= 4) return 4; // Major forex pairs
  if (maxPrecision <= 5) return 5; // Minor forex pairs, crypto

  return Math.min(maxPrecision, 8); // Cap at 8 decimal places
}

// Format price with dynamic precision
export function formatPrice(price: number, precision?: number): string {
  const decimals = precision ?? 5; // Use provided precision or default
  return price.toFixed(decimals);
}

// Format price with auto-detected precision from context
export function formatPriceWithPrecision(price: number, contextPrices: number[]): string {
  const precision = detectPricePrecision(contextPrices);
  return formatPrice(price, precision);
}

// Bid/Ask Price Calculation Functions

/**
 * Calculate bid/ask prices from tick data
 */
export function getBidAskFromTick(tick: TickData): BidAskPrice | null {
  // Direct bid/ask from tick data
  if (tick.bid !== undefined && tick.ask !== undefined) {
    const spread = tick.ask - tick.bid;
    const mid = (tick.bid + tick.ask) / 2;
    
    return {
      bid: tick.bid,
      ask: tick.ask,
      spread,
      timestamp: tick.timestamp,
      mid
    };
  }
  
  // If only one price is available, estimate the other using a default spread
  if (tick.bid !== undefined && tick.ask === undefined) {
    // Use 2 pips as default spread for major pairs (adjust as needed)
    const defaultSpread = 0.00002;
    const ask = tick.bid + defaultSpread;
    
    return {
      bid: tick.bid,
      ask,
      spread: defaultSpread,
      timestamp: tick.timestamp,
      mid: (tick.bid + ask) / 2
    };
  }
  
  if (tick.ask !== undefined && tick.bid === undefined) {
    const defaultSpread = 0.00002;
    const bid = tick.ask - defaultSpread;
    
    return {
      bid,
      ask: tick.ask,
      spread: defaultSpread,
      timestamp: tick.timestamp,
      mid: (bid + tick.ask) / 2
    };
  }
  
  // If we have last price, use it as mid and create spread
  if (tick.last !== undefined) {
    const defaultSpread = 0.00002;
    const halfSpread = defaultSpread / 2;
    const bid = tick.last - halfSpread;
    const ask = tick.last + halfSpread;
    
    return {
      bid,
      ask,
      spread: defaultSpread,
      timestamp: tick.timestamp,
      mid: tick.last
    };
  }
  
  return null;
}

/**
 * Calculate bid/ask prices from candle data using spread information
 */
export function getBidAskFromCandle(candle: CandleData, currentPrice?: number): BidAskPrice {
  // Use current price if provided, otherwise use close
  const midPrice = currentPrice !== undefined ? currentPrice : candle.close;
  
  // Enhanced spread conversion with better detection
  let spreadInPrice: number;
  
  if (candle.spread <= 0) {
    // Default spread for major pairs if no spread data
    spreadInPrice = getDefaultSpreadForPrice(midPrice);
  } else if (candle.spread < 1) {
    // Spread is already in price format (e.g., 0.00020)
    spreadInPrice = candle.spread;
  } else if (candle.spread < 100) {
    // Spread in pips (e.g., 2.0 pips = 0.00020 for EUR/USD)
    spreadInPrice = candle.spread * 0.0001;
  } else {
    // Spread in fractional pips (e.g., 20 = 2.0 pips = 0.00020 for EUR/USD)
    spreadInPrice = candle.spread * 0.00001;
  }
  
  const halfSpread = spreadInPrice / 2;
  const bid = midPrice - halfSpread;
  const ask = midPrice + halfSpread;
  
  return {
    bid,
    ask,
    spread: spreadInPrice,
    timestamp: candle.timestamp,
    mid: midPrice
  };
}

/**
 * Get default spread based on the price level (currency pair detection)
 */
function getDefaultSpreadForPrice(price: number): number {
  if (price > 10) {
    // Major pairs like USD/JPY (e.g., 150.00)
    return 0.001; // 0.1 pips
  } else if (price > 1) {
    // Major pairs like EUR/USD, GBP/USD (e.g., 1.1000)
    return 0.00002; // 0.2 pips
  } else if (price > 0.1) {
    // Some exotic pairs (e.g., 0.7500)
    return 0.00002; // 0.2 pips
  } else {
    // Very exotic or crypto pairs
    return price * 0.0001; // 0.01% of price
  }
}

/**
 * Get price quote for order execution
 */
export function getPriceQuote(bidAsk: BidAskPrice): PriceQuote {
  return {
    buy: bidAsk.ask,   // Buy at ask price
    sell: bidAsk.bid,  // Sell at bid price
    spread: bidAsk.spread,
    timestamp: bidAsk.timestamp
  };
}

/**
 * Calculate execution price for an order based on order type
 */
export function getExecutionPrice(orderType: OrderType, bidAsk: BidAskPrice): number {
  const quote = getPriceQuote(bidAsk);
  return orderType === 'buy' ? quote.buy : quote.sell;
}

/**
 * Calculate realistic P&L considering bid/ask spread on entry and exit
 */
export function calculateRealisticPnL(
  orderType: OrderType,
  size: number,
  entryBidAsk: BidAskPrice,
  exitBidAsk: BidAskPrice,
  commission: number = 0
): number {
  const entryPrice = getExecutionPrice(orderType, entryBidAsk);
  const exitPrice = getExecutionPrice(orderType === 'buy' ? 'sell' : 'buy', exitBidAsk);
  
  const priceDiff = orderType === 'buy'
    ? exitPrice - entryPrice
    : entryPrice - exitPrice;
  
  const grossPnL = priceDiff * size * 100000; // Convert to base currency
  return grossPnL - commission;
}

/**
 * Check if stop loss or take profit should be triggered considering bid/ask prices
 */
export function checkStopLossTakeProfitBidAsk(
  position: Position,
  bidAsk: BidAskPrice
): { shouldClose: boolean; reason?: string; exitPrice?: number } {
  // For stop loss/take profit, we need to check against the price that would be used for closing
  const currentMarketPrice = position.type === 'buy' ? bidAsk.bid : bidAsk.ask;
  
  if (position.stopLoss) {
    const stopLossTriggered = position.type === 'buy'
      ? currentMarketPrice <= position.stopLoss
      : currentMarketPrice >= position.stopLoss;

    if (stopLossTriggered) {
      return { 
        shouldClose: true, 
        reason: 'Stop Loss',
        exitPrice: currentMarketPrice
      };
    }
  }

  if (position.takeProfit) {
    const takeProfitTriggered = position.type === 'buy'
      ? currentMarketPrice >= position.takeProfit
      : currentMarketPrice <= position.takeProfit;

    if (takeProfitTriggered) {
      return { 
        shouldClose: true, 
        reason: 'Take Profit',
        exitPrice: currentMarketPrice
      };
    }
  }

  return { shouldClose: false };
}

/**
 * Calculate unrealized P&L using bid/ask prices
 */
export function calculateUnrealizedPnLBidAsk(position: Position, bidAsk: BidAskPrice): number {
  // Current market price for closing the position
  const currentPrice = position.type === 'buy' ? bidAsk.bid : bidAsk.ask;
  
  const priceDiff = position.type === 'buy'
    ? currentPrice - position.entryPrice
    : position.entryPrice - currentPrice;

  return priceDiff * position.size * 100000; // Convert to base currency
}
